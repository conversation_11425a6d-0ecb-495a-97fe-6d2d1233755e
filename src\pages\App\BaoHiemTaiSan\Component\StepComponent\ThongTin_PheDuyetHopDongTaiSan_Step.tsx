import {FilePdfOutlined} from "@ant-design/icons";
import {Viewer, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {zoomPlugin} from "@react-pdf-viewer/zoom";
import "@react-pdf-viewer/zoom/lib/styles/index.css";
import {ReactQuery} from "@src/@types";
import {Button} from "@src/components";
import {useAsyncAction} from "@src/hooks/useAsyncAction";
import {formatCurrencyUS} from "@src/utils";
import {Col, message, Row} from "antd";
import {isEqual} from "lodash";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {memo, useCallback, useEffect, useRef, useState} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {RenderTableDoiTuongBaoHiemTaiSan_Component} from "../RenderTableDoiTuongBaoHemTaiSan_Component";
import {tableDoiTuongColumn, TableDoiTuongColumnDataType} from "./Constant";
import {ThongTinCauHinhBaoHiemTaiSanStep} from "./ThongTin_CauHinhBaoHiemTaiSan_Step";

// Hook tính pageSize động theo chiều cao màn hình
const useDynamicPageSize = () => {
  const [pageSize, setPageSize] = useState(10);

  useEffect(() => {
    function calculatePageSize() {
      // Giả sử header, search form, ... chiếm 300px, mỗi dòng table 48px
      const availableHeight = window.innerHeight - 300;
      const rowHeight = 48;
      const calculatedPageSize = Math.max(3, Math.floor(availableHeight / rowHeight)) + 5; // tối thiểu 3 dòng + thêm 10
      setPageSize(calculatedPageSize);
    }

    calculatePageSize();
    window.addEventListener("resize", calculatePageSize);
    return () => window.removeEventListener("resize", calculatePageSize);
  }, []);

  return pageSize;
};

interface FileCategory {
  id: string;
  name: string;
  files: {type: string; url: string; name: string; id?: number}[];
}

// Custom hook để xử lý zoom và pan (loại bỏ điều hướng trang để tránh xung đột)
const usePdfZoomPan = (zoomPluginInstance: any, minZoom = 0.5) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({x: 0, y: 0});
  const [zoomLevel, setZoomLevel] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  // Xử lý cuộn chuột - Ctrl+Cuộn = Phóng to/thu nhỏ, Cuộn = Cuộn tự nhiên
  const handleWheel = useCallback(
    (e: WheelEvent) => {
      const isCtrlPressed = e.ctrlKey || e.metaKey;

      if (isCtrlPressed) {
        // Chế độ zoom khi giữ Ctrl
        e.preventDefault();
        const isScrollDown = e.deltaY > 0;
        const zoomStep = 0.1;
        const delta = isScrollDown ? -zoomStep : zoomStep;
        const newZoom = Math.max(minZoom, Math.min(5, zoomLevel + delta));

        if (zoomPluginInstance && zoomPluginInstance.zoomTo) {
          zoomPluginInstance.zoomTo(newZoom);
          setZoomLevel(newZoom);
        }
      }
      // Loại bỏ hoàn toàn logic tự động chuyển trang
      // Để PDF viewer xử lý cuộn tự nhiên mà không can thiệp
    },
    [zoomLevel, zoomPluginInstance, minZoom],
  );

  // Xử lý phóng to/thu nhỏ bằng bàn phím (loại bỏ điều hướng trang để tránh can thiệp)
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.target && (e.target as HTMLElement).tagName === "INPUT") return;

      // Chỉ xử lý phím tắt zoom, loại bỏ điều hướng trang để tránh xung đột

      // Phím tắt zoom
      let newZoom = zoomLevel;

      if (e.key === "+" || e.key === "=" || (e.ctrlKey && e.key === "+")) {
        e.preventDefault();
        newZoom = Math.min(5, zoomLevel + 0.2);
      } else if (e.key === "-" || (e.ctrlKey && e.key === "-")) {
        e.preventDefault();
        newZoom = Math.max(minZoom, zoomLevel - 0.2);
      } else if (e.key === "0" || (e.ctrlKey && e.key === "0")) {
        e.preventDefault();
        newZoom = 1;
      }

      if (newZoom !== zoomLevel && zoomPluginInstance && zoomPluginInstance.zoomTo) {
        zoomPluginInstance.zoomTo(newZoom);
        setZoomLevel(newZoom);
      }
    },
    [zoomLevel, zoomPluginInstance, minZoom],
  );

  // Xử lý double click để fit-to-width hoặc zoom 100%
  const handleDoubleClick = useCallback(() => {
    if (!zoomPluginInstance) return;

    const newZoom = zoomLevel === 1 ? 1.5 : 1; // Toggle giữa 100% và 150%
    zoomPluginInstance.zoomTo(newZoom);
    setZoomLevel(newZoom);
  }, [zoomLevel, zoomPluginInstance]);

  // Xử lý pan bằng chuột
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (zoomLevel > 1 && e.button === 0) {
        // Chỉ xử lý left click
        setIsPanning(true);
        setLastMousePos({x: e.clientX, y: e.clientY});
        e.preventDefault();
        e.stopPropagation();
      }
    },
    [zoomLevel],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isPanning || !containerRef.current) return;

      const deltaX = e.clientX - lastMousePos.x;
      const deltaY = e.clientY - lastMousePos.y;

      // Tìm container có thể scroll của PDF viewer
      const scrollContainer = containerRef.current.querySelector(".rpv-core__inner-pages") || containerRef.current.querySelector(".rpv-core__viewer") || containerRef.current;

      if (scrollContainer && typeof (scrollContainer as any).scrollBy === "function") {
        (scrollContainer as any).scrollBy(-deltaX, -deltaY);
      } else if (scrollContainer) {
        (scrollContainer as any).scrollLeft -= deltaX;
        (scrollContainer as any).scrollTop -= deltaY;
      }

      setLastMousePos({x: e.clientX, y: e.clientY});
      e.preventDefault();
    },
    [isPanning, lastMousePos],
  );

  const handleMouseUp = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning) {
        setIsPanning(false);
        e.preventDefault();
      }
    },
    [isPanning],
  );

  // Xử lý mouse leave để dừng panning
  const handleMouseLeave = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Global mouse up handler cho document
  const handleGlobalMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Loại bỏ effect reset scroll để tránh can thiệp vào scroll tự nhiên
  // PDF viewer sẽ tự xử lý scroll position khi chuyển trang

  // Effect để thêm event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("wheel", handleWheel, {passive: false});
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("mouseup", handleGlobalMouseUp);

    return () => {
      container.removeEventListener("wheel", handleWheel);
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
    };
  }, [handleWheel, handleKeyDown, handleGlobalMouseUp]);

  return {
    containerRef,
    isPanning,
    zoomLevel,
    currentPage,
    totalPages,
    handleDoubleClick,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    setZoomLevel,
    setCurrentPage,
    setTotalPages,
  };
};

const ThongTinPheDuyetHopDongTaiSanStepComponent = ({onDownloadPDF}: {onDownloadPDF?: (downloadFn: () => void, hasFile: boolean) => void}) => {
  // --- BẮT ĐẦU: Logic lấy danh sách đối tượng xe giống step 2 ---
  const {layChiTietDoiTuongBaoHiemTaiSan, chiTietHopDongBaoHiemTaiSan, getDanhSachFileThumbnailTheoDoiTuong, exportPdfHopDong, danhSachDoiTuongBaoHiemTaiSan} = useBaoHiemTaiSanContext();
  const [doiTuongXeSelected, setDoiTuongXeSelected] = useState<any>(null);

  // Sử dụng hook tính pageSize động
  const dynamicPageSize = useDynamicPageSize();

  // State lưu categories lấy từ API
  const [categories, setCategories] = useState<FileCategory[]>([]);

  // State lưu selectedFile dựa trên categories thực tế
  const [selectedFile, setSelectedFile] = useState<any>(undefined);

  // Zoom plugin cho PDF Viewer
  const zoomPluginInstance = zoomPlugin();

  // State để lưu totalPages và tính toán minZoom
  const [totalPages, setTotalPages] = useState(0);
  const minZoom = totalPages <= 1 ? 0.8 : 0.5; // 80% nếu 1 page, 50% nếu nhiều hơn 1 page

  // Sử dụng custom hook cho zoom, pan và page navigation
  // Tạo lại hook khi minZoom thay đổi để cập nhật logic zoom
  const {
    containerRef,
    isPanning,
    zoomLevel,
    // currentPage,
    handleDoubleClick,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    setZoomLevel,
    setCurrentPage,
  } = usePdfZoomPan(zoomPluginInstance, minZoom);

  // Effect để reset zoom và page info khi file thay đổi
  useEffect(() => {
    if (selectedFile) {
      setZoomLevel(1);
      setCurrentPage(1);
      setTotalPages(0);
    }
  }, [selectedFile, setZoomLevel, setCurrentPage]);

  // Effect để đảm bảo zoom level không vi phạm minZoom khi totalPages thay đổi
  useEffect(() => {
    if (zoomLevel < minZoom && zoomPluginInstance && zoomPluginInstance.zoomTo) {
      zoomPluginInstance.zoomTo(minZoom);
      setZoomLevel(minZoom);
    }
  }, [minZoom, zoomLevel, zoomPluginInstance, setZoomLevel]);

  // Khi categories thay đổi, tự động chọn file đầu tiên nếu có
  useEffect(() => {
    if (categories.length > 0) {
      setSelectedFile(categories[0].files[0]);
    } else {
      setSelectedFile(undefined);
    }
  }, [categories]);

  // Ghi nhớ hàm async để tránh vòng lặp vô hạn
  const viewHopDongAsyncFn = useCallback(async () => {
    if (!chiTietHopDongBaoHiemTaiSan?.so_id) {
      message.error("Không có thông tin hợp đồng.");
      return;
    }

    try {
      const paramsXemFileHD = {
        ma_doi_tac_ql: "ESCS",
        so_id: chiTietHopDongBaoHiemTaiSan.so_id,
        template: chiTietHopDongBaoHiemTaiSan.file_hd || "",
      };
      const pdfUrl = await exportPdfHopDong(paramsXemFileHD);
      if (pdfUrl) {
        // Cập nhật selectedFile để hiển thị PDF hợp đồng
        setSelectedFile({
          type: "pdf",
          url: pdfUrl,
          name: `Hợp đồng ${chiTietHopDongBaoHiemTaiSan.so_hd || chiTietHopDongBaoHiemTaiSan.so_id}`,
        });
        // message.success("Tải hợp đồng thành công!");
      } else {
        console.error("❌ pdfUrl is null or undefined");
      }
    } catch (error) {
      console.error("Lỗi khi tải hợp đồng PDF:", error);
      message.error("Có lỗi xảy ra khi tải hợp đồng PDF!");
      throw error; // Re-throw để useAsyncAction có thể handle
    }
  }, [chiTietHopDongBaoHiemTaiSan?.so_id, chiTietHopDongBaoHiemTaiSan?.file_hd, chiTietHopDongBaoHiemTaiSan?.so_hd, exportPdfHopDong]);

  // Triển khai async action cho việc xem hợp đồng PDF sử dụng useAsyncAction hook
  const [handleViewHopDong, isLoadingViewHopDong] = useAsyncAction(viewHopDongAsyncFn);

  // Ghi nhớ hàm async cho GCN để tránh vòng lặp vô hạn
  const viewGCNAsyncFn = useCallback(
    async (record: TableDoiTuongColumnDataType) => {
      if (!record?.so_id_dt) {
        message.error("Không có thông tin đối tượng.");
        return;
      }

      try {
        const paramsXemFileGCN = {
          ma_doi_tac_ql: "ESCS",
          so_id: chiTietHopDongBaoHiemTaiSan?.so_id,
          so_id_dt: record?.so_id_dt ? Number(record.so_id_dt) : undefined,
          template: chiTietHopDongBaoHiemTaiSan?.file_gcn || "",
        };
        const pdfUrl = await exportPdfHopDong(paramsXemFileGCN);
        if (pdfUrl) {
          // Cập nhật selectedFile để hiển thị PDF GCN
          setSelectedFile({
            type: "pdf",
            url: pdfUrl,
            name: `GCN ${record?.so_id_dt}`,
          });
          message.success("Tải giấy chứng nhận thành công!");
        } else {
          console.error("❌ pdfUrl is null or undefined");
        }
      } catch (error) {
        console.error("Lỗi khi tải GCN PDF:", error);
        message.error("Có lỗi xảy ra khi tải giấy chứng nhận PDF!");
        throw error; // Re-throw để useAsyncAction có thể handle
      }
    },
    [chiTietHopDongBaoHiemTaiSan?.so_id, chiTietHopDongBaoHiemTaiSan?.file_gcn, exportPdfHopDong],
  );

  // Triển khai async action cho việc xem GCN PDF sử dụng useAsyncAction hook
  const [handleViewGCN, isLoadingViewGCN] = useAsyncAction(viewGCNAsyncFn);

  //bấm chọn 1 đối tượng - hiển thị GCN
  const handleSelectDoiTuongBaoHiemXe = useCallback(
    async (record: TableDoiTuongColumnDataType) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      const [chiTietDoiTuong] = await Promise.all([layChiTietDoiTuongBaoHiemTaiSan(record as ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams), handleViewGCN(record)]);
      setDoiTuongXeSelected(chiTietDoiTuong || null);
    },
    [layChiTietDoiTuongBaoHiemTaiSan, handleViewGCN],
  );

  // Tự động hiển thị hợp đồng khi vào màn hình
  // Loại bỏ handleViewHopDong khỏi dependencies để tránh vòng lặp vô hạn
  useEffect(() => {
    if (chiTietHopDongBaoHiemTaiSan?.so_id) {
      handleViewHopDong();
    }
  }, [chiTietHopDongBaoHiemTaiSan?.so_id]); // Chỉ phụ thuộc vào so_id để tránh vòng lặp

  // Hàm xử lý download PDF
  const handleDownloadPDF = () => {
    if (selectedFile && selectedFile.type === "pdf" && selectedFile.url) {
      const link = document.createElement("a");
      link.href = selectedFile.url;
      link.download = selectedFile.name ? `${selectedFile.name}.pdf` : "file.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning("Không có file PDF để tải xuống.");
    }
  };

  // Effect để thông báo cho parent component về download function
  useEffect(() => {
    if (onDownloadPDF) {
      const hasFile = selectedFile && selectedFile.type === "pdf" && selectedFile.url;
      onDownloadPDF(handleDownloadPDF, !!hasFile);
    }
  }, [selectedFile, onDownloadPDF]);

  return (
    <Row gutter={16} className="mt-4 h-full overflow-hidden border border-[#eee]">
      {/* Cột trái: PDF Viewer với zoom/pan */}
      <Col span={11} className="flex h-[90%] flex-col overflow-hidden !pl-0" style={{borderBottom: "1px solid #eee", borderLeft: "1px solid #eee"}}>
        <div className="relative flex min-h-[65vh] flex-col overflow-hidden">
          {selectedFile && selectedFile.type === "pdf" ? (
            <div className="flex h-full w-full flex-col">
              {/* Zoom controls với thông tin zoom level và page navigation */}

              {/* PDF Viewer với zoom/pan tương tác */}
              <div
                ref={containerRef}
                className={`min-h-0 flex-1 overflow-hidden rounded-b-lg border border-[#333] shadow-[0_2px_8px_rgba(0,0,0,0.1)] ${
                  zoomLevel > 1 ? (isPanning ? "cursor-grabbing" : "cursor-grab") : "cursor-default"
                }`}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
                onDoubleClick={handleDoubleClick}
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                }}>
                <Worker workerUrl={workerUrl}>
                  <Viewer
                    fileUrl={selectedFile.url}
                    plugins={[zoomPluginInstance]}
                    onDocumentLoad={e => {
                      // Reset zoom level và page info khi load document mới
                      setZoomLevel(1);
                      setCurrentPage(1);
                      setTotalPages(e.doc.numPages);
                    }}
                    onPageChange={e => {
                      // Cập nhật current page khi user navigate - chỉ để theo dõi, không can thiệp
                      setCurrentPage(e.currentPage + 1); // react-pdf-viewer sử dụng 0-based index
                    }}
                  />
                </Worker>
              </div>
              {/* Hướng dẫn sử dụng và trạng thái */}
              {/* Hướng dẫn sử dụng và trạng thái */}
              {/* <div className="text-xs text-gray-500 p-2 bg-gray-50 border-t">
                <div className="flex justify-between items-center">
                  <div className="flex flex-wrap gap-4">
                    <span>🖱️ Ctrl+Scroll: Zoom</span>
                    <span>🖱️ Scroll: Chuyển trang</span>
                    <span>🖱️ Double-click: Toggle zoom</span>
                    {zoomLevel > 1 && <span>🖱️ Drag: Pan</span>}
                    <span>⌨️ ←/→: Chuyển trang</span>
                    <span>⌨️ Home/End: Trang đầu/cuối</span>
                    <span>⌨️ +/-: Zoom</span>
                    <span>⌨️ 0: Reset</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {isPanning && (
                      <div className="flex items-center gap-1 text-blue-600 font-medium">
                        <span>🤏</span>
                        <span>Đang di chuyển...</span>
                      </div>
                    )}
                  </div>
                </div>
              </div> */}
            </div>
          ) : (
            <div className="flex h-full w-full flex-col">
              {/* Header placeholder để match với PDF viewer header */}
              <div className="flex items-center justify-between gap-2 rounded-t-lg border-b border-[#eee] bg-[#fafafa] p-2">
                <div className="flex items-center gap-2">
                  <div className="text-gray-600 bg-white rounded border px-2 py-1 text-xs">No PDF</div>
                </div>
              </div>
              {/* Main placeholder container để match với PDF viewer container */}
              <div className="flex min-h-0 flex-1 flex-col items-center justify-center overflow-hidden rounded-b-lg border border-[#333] text-center shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
                <div style={{fontSize: 48, opacity: 0.5, color: "#999"}}>📄</div>
                <div style={{marginBottom: 8, color: "#999", fontSize: 16}}>Chưa có file PDF nào được chọn</div>
              </div>
            </div>
          )}
        </div>
      </Col>

      {/* Cột giữa: Xem hình ảnh/PDF */}
      <Col span={7} className="flex h-[90%] flex-col overflow-hidden !px-0">
        {/* Thông tin chi tiết đối tượng xe được chọn */}
        {chiTietHopDongBaoHiemTaiSan && (
          <div className="rounded border border-[#d9d9d9] bg-[#f6f6f6] p-3 shadow-sm">
            {/* <div style={{fontWeight: 600, fontSize: 16, marginBottom: 8}}>Thông tin đối tượng xe</div> */}
            {(() => {
              const thongTinXe = [
                {label: "Khách hàng", value: chiTietHopDongBaoHiemTaiSan.ten_kh || "-"},
                {label: "Đối tác cấp đơn", value: chiTietHopDongBaoHiemTaiSan.ten_doi_tac_ql || "-"},
                {label: "Số hợp đồng", value: chiTietHopDongBaoHiemTaiSan.so_hd || "-"},
                {
                  label: "Hiệu lực",
                  value: chiTietHopDongBaoHiemTaiSan.ngay_hl
                    ? `${chiTietHopDongBaoHiemTaiSan.gio_hl || ""} ${chiTietHopDongBaoHiemTaiSan.ngay_hl} - ${chiTietHopDongBaoHiemTaiSan.gio_kt || ""} ${chiTietHopDongBaoHiemTaiSan.ngay_kt || ""}`
                    : "-",
                },
                {label: "Loại hợp đồng", value: chiTietHopDongBaoHiemTaiSan.vip || "Không Vip"},
                // Thêm các trường khác nếu cần
              ];

              // Thông tin sản phẩm và phí để hiển thị trên cùng một dòng
              const sanPham = chiTietHopDongBaoHiemTaiSan.ten_sp || "-";
              const tongPhi = chiTietHopDongBaoHiemTaiSan.tong_phi ? formatCurrencyUS(chiTietHopDongBaoHiemTaiSan.tong_phi) : "-";
              return (
                <div>
                  <div className="flex flex-col gap-2">
                    {thongTinXe.map((item: any, idx: number) => (
                      <div key={idx} className="text-[11px]">
                        <b>{item.label}:</b> {item.value}
                      </div>
                    ))}
                    {/* Sản phẩm và Tổng phí BH trên cùng một dòng */}
                    <div className="flex gap-4 text-[11px]">
                      <span>
                        <b>Sản phẩm:</b> {sanPham}
                      </span>
                      <span>
                        <b>Tổng phí BH:</b> {tongPhi}
                      </span>
                    </div>
                  </div>
                  <div className="ml-0 mr-auto">
                    <Button
                      type="link"
                      className="!p-0 !font-semibold !text-[#7ac142]"
                      icon={<FilePdfOutlined />}
                      onClick={handleViewHopDong}
                      loading={isLoadingViewHopDong}
                      disabled={isLoadingViewHopDong}>
                      Xem hợp đồng
                    </Button>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
        {/* <ThongTinCauHinhBaoHiemTaiSanStep disabled={true} pageSize={20} /> */}
        <ThongTinCauHinhBaoHiemTaiSanStep disabled={true} pageSize={20} />
        {/* RenderTabCauHinhTaiBaoHiem */}
      </Col>

      {/* Cột phải: Danh sách hạng mục và thumbnail */}
      <Col span={6} className="flex h-[90%] flex-col overflow-hidden">
        <RenderTableDoiTuongBaoHiemTaiSan_Component columns={tableDoiTuongColumn || []} onRowClick={handleSelectDoiTuongBaoHiemXe} pageSize={dynamicPageSize} />
      </Col>
    </Row>
  );
};
ThongTinPheDuyetHopDongTaiSanStepComponent.displayName = "ThongTinPheDuyetHopDongTaiSanStepComponent";
export const ThongTinPheDuyetHopDongTaiSanStep = memo(ThongTinPheDuyetHopDongTaiSanStepComponent, isEqual);
