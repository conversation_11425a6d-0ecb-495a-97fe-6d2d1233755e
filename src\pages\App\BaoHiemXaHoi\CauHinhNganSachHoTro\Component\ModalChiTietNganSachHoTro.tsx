import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {
  ngayApDungColumns,
  //   cauHoiColumns,
  //   DataIndexCauHoi,
  //   DataIndexNgayApDung,
  //   FormThemNgayApDung,
  IModalChiTietNganSachHoTroRef,
  Props,
  TableNgayApDungDataType,
  FormThemNgayApDung,
  DataIndexNgayApDung,
  FormChiTietNganSachHoTro,
  //   TableCauHoiDataType,
} from "./index.configs";
import {Col, Dropdown, Flex, Form, InputRef, Modal, Row, Space, Table, TableColumnType} from "antd";
import {useCauHinhNganSachHoTroContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, Highlighter, Popcomfirm, TableFilterDropdown} from "@src/components";
import {CheckOutlined, CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {ReactQuery} from "@src/@types";
// import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
// const {ma_doi_tac_ql, ten, nv} = FormchiTietTinhThanh;

const ModalChiTietNganSachHoTroComponent = forwardRef<IModalChiTietNganSachHoTroRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucTinhThanh?: CommonExecute.Execute.IDanhMucTinhThanh) => {
      setIsOpen(true);
      //   if (dataDanhMucTinhThanh) setchiTietTinhThanh(dataDanhMucTinhThanh);
    },
    close: () => {
      setIsOpen(false);
      //   setchiTietTinhThanh(null);
      //   setDanhSachCauHoi([]);
    },
  }));
  //   const refModalThemCauHoi = useRef<IModalThemCauHoiRef>(null);
  const {ngay_ad} = FormThemNgayApDung;
  const {ty_le_nsnn, ty_le_nsdp, ma_sp} = FormChiTietNganSachHoTro;
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(8);
  const {
    loading,
    filterParams,
    danhSachNganSachHoTroNgayApDung,
    chiTietTinhThanh,
    chiTietNganSachHoTro,
    layDanhSachNganSachHoTroNgayApDung,
    layChiTietNganSachHoTro,
    CapNhatNganSachHoTro,
    xoaNgayApDungNganSachHoTro,
    updateNganSachHoTroNgayApDung,
  } = useCauHinhNganSachHoTroContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [formBHXH] = Form.useForm();
  const [formBHYT] = Form.useForm();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [formThemNgayApDung] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const [ngayAdMoiTao, setNgayAdMoiTao] = useState<string | null>(null);
  const [selectedNgayApDung, setSelectedNgayApDung] = useState<string | null>(null);

  useEffect(() => {
    console.log("chiTietNganSachHoTro:", chiTietNganSachHoTro);

    if (chiTietNganSachHoTro) {
      let bhxhData = {};
      let bhytData = {};

      // Kiểm tra nếu chiTietNganSachHoTro là mảng
      if (Array.isArray(chiTietNganSachHoTro)) {
        // Tìm dữ liệu theo ma_sp
        bhxhData = chiTietNganSachHoTro.find(item => item.ma_sp === "BHXH") || {};
        bhytData = chiTietNganSachHoTro.find(item => item.ma_sp === "BHYT") || {};
      } else if (typeof chiTietNganSachHoTro === "object") {
        // Nếu là object, kiểm tra xem có phải là dữ liệu đơn lẻ không
        if (chiTietNganSachHoTro.ma_sp === "BHXH") {
          bhxhData = chiTietNganSachHoTro;
        } else if (chiTietNganSachHoTro.ma_sp === "BHYT") {
          bhytData = chiTietNganSachHoTro;
        }
      }
      formBHXH.setFieldsValue({
        ty_le_nsnn: (bhxhData as any).ty_le_nsnn,
        ty_le_nsdp: (bhxhData as any).ty_le_nsdp,
      });

      formBHYT.setFieldsValue({
        ty_le_nsnn: (bhytData as any).ty_le_nsnn,
        ty_le_nsdp: (bhytData as any).ty_le_nsdp,
      });
    }
  }, [chiTietNganSachHoTro, formBHXH, formBHYT]);
  useEffect(() => {
    // Reset form khi selectedNgayApDung thay đổi
    formBHXH.resetFields();
    formBHYT.resetFields();
  }, [selectedNgayApDung, formBHXH, formBHYT]);
  useEffect(() => {
    if (chiTietTinhThanh?.ma) {
      layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
    }
  }, [chiTietTinhThanh]);

  useEffect(() => {
    if (selectedNgayApDung !== null) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectedNgayApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (danhSachNganSachHoTroNgayApDung.length > 0) {
      let selected;
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        selected = danhSachNganSachHoTroNgayApDung.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));
        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachNganSachHoTroNgayApDung.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachNganSachHoTroNgayApDung.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.ngay_ad !== undefined && selected.ngay_ad !== null) {
        setSelectedNgayApDung(String(selected.ngay_ad));
        console.log("selected.ngay_ad", selected.ngay_ad);
        layChiTietNganSachHoTro({ma_tinh: chiTietTinhThanh.ma, ngay_ad: Number(dayjs(selected.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD"))});
      }
    } else {
      setSelectedNgayApDung(null);
    }
  }, [danhSachNganSachHoTroNgayApDung, chiTietTinhThanh]);
  const closeModal = () => {
    setIsOpen(false);
    // setchiTietTinhThanh(null);
    // setDanhSachCauHoi([]);

    // form.resetFields();
    formBHXH.resetFields();
    formBHYT.resetFields();
    // setFilterParams(filterParams);
  };
  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableNgayApDungDataType>>(() => {
    try {
      const mappedData = danhSachNganSachHoTroNgayApDung.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        ngay_ad: item.ngay_ad || "",
        ma_tinh: item.ma_tinh,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(item.ngay_ad),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachNganSachHoTroNgayApDung, pageSize]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleNgayApDungRowClick = async (record: TableNgayApDungDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || record.ngay_ad === undefined || record.ngay_ad === null) {
      setSelectedNgayApDung(null);
      return;
    }

    console.log("record", record);
    setSelectedNgayApDung(record.ngay_ad);
    layChiTietNganSachHoTro({ma_tinh: chiTietTinhThanh.ma, ngay_ad: Number(dayjs(record.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD"))});
  };
  const handleSubmit = async () => {
    try {
      const values = await formThemNgayApDung.validateFields();

      if (!values.ngay_ad) {
        console.log("Vui lòng chọn ngày áp dụng");
        return;
      }
      console.log("chiTietTinhThanh", chiTietTinhThanh);
      await updateNganSachHoTroNgayApDung({
        ma_tinh: chiTietTinhThanh?.ma,
        ngay_ad: Number(dayjs(values.ngay_ad).format("YYYYMMDD")),
      });
      setDropdownOpen(false); // Đóng dropdown sau khi lưu thành công
      formThemNgayApDung.resetFields();
      setNgayAdMoiTao(values.ngay_ad); // Lưu lại ngày vừa tạo

      if (chiTietTinhThanh?.ma) {
        layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
      }
    } catch (error) {
      console.log("Lỗi khi submit:", error);
      // Không đóng dropdown nếu có lỗi để người dùng có thể sửa
    }
  };
  const handleDelete = async (ngay_ad: number) => {
    try {
      // TODO: Implement delete function - onDeleteCauHoiApDung is not available
      console.log("Delete ngay_ad:", ngay_ad);
      await xoaNgayApDungNganSachHoTro({
        ngay_ad: Number(dayjs(ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")),
        ma_tinh: chiTietTinhThanh?.ma,
      });

      // Reset selection sau khi xóa
      setSelectedNgayApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietTinhThanh?.ma) {
        layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };
  // const onPressLuuNganSachHoTro = async () => {
  //   try {
  //     const valuesBHXH = await formBHXH.validateFields();
  //     const valuesBHYT = await formBHYT.validateFields();

  //     const params: ReactQuery.ICapNhatNganSachHoTroParams = {
  //       ma_tinh: chiTietTinhThanh?.ma,
  //       ngay_ad: selectedNgayApDung ? Number(dayjs(selectedNgayApDung, "DD/MM/YYYY").format("YYYYMMDD")) : undefined,
  //       ns_ct: [
  //         {
  //           ma_sp: "BHXH",
  //           ty_le_nsnn: valuesBHXH.ty_le_nsnn,
  //           ty_le_nsdp: valuesBHXH.ty_le_nsdp,
  //         },
  //         {
  //           ma_sp: "BHYT",
  //           ty_le_nsnn: valuesBHYT.ty_le_nsnn,
  //           ty_le_nsdp: valuesBHYT.ty_le_nsdp,
  //         },
  //       ],
  //     };
  //     console.log("params", params);
  //     await CapNhatNganSachHoTro(params);
  //     // if (chiTietTinhThanh?.ma) {
  //     //   layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma});
  //     // }
  //     console.log("Lưu thành công:", params);
  //   } catch (error) {
  //     console.log("Lỗi khi submit:", error);
  //   }
  // };
  const onPressLuuNganSachHoTro = async () => {
    try {
      const valuesBHXH = await formBHXH.validateFields().catch(() => null);
      const valuesBHYT = await formBHYT.validateFields().catch(() => null);
      console.log("valuesBHXH", valuesBHXH);
      console.log("valuesBHYT", valuesBHYT);
      const ns_ct: any[] = [];

      if (valuesBHXH.ty_le_nsnn && valuesBHXH.ty_le_nsdp) {
        ns_ct.push({
          ma_sp: "BHXH",
          ty_le_nsnn: valuesBHXH.ty_le_nsnn,
          ty_le_nsdp: valuesBHXH.ty_le_nsdp,
        });
      }

      if (valuesBHYT && valuesBHYT.ty_le_nsnn && valuesBHYT.ty_le_nsdp) {
        ns_ct.push({
          ma_sp: "BHYT",
          ty_le_nsnn: valuesBHYT.ty_le_nsnn,
          ty_le_nsdp: valuesBHYT.ty_le_nsdp,
        });
      }

      const params: ReactQuery.ICapNhatNganSachHoTroParams = {
        ma_tinh: chiTietTinhThanh?.ma,
        ngay_ad: selectedNgayApDung ? Number(dayjs(selectedNgayApDung, "DD/MM/YYYY").format("YYYYMMDD")) : undefined,
        ns_ct,
      };

      console.log("params", params);
      await CapNhatNganSachHoTro(params);
      console.log("Lưu thành công:", params);
    } catch (error) {
      console.log("Lỗi khi submit:", error);
    }
  };

  const getColumnSearchCauHoiApDungProps = (dataIndex: DataIndexNgayApDung, title: string): TableColumnType<DataIndexNgayApDung> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexNgayApDung]
        ? record[dataIndex as keyof DataIndexNgayApDung]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });

  const renderDeleteButton = (ngay_ad?: number) => {
    if (!ngay_ad) return null;
    return (
      <div>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(ngay_ad)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };

  const renderTableCauHoiApDungFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          <Space className="">
            <Dropdown
              className=""
              open={dropdownOpen}
              onOpenChange={setDropdownOpen}
              trigger={["click"]}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
              dropdownRender={() => (
                <div style={{padding: 8, display: "flex"}}>
                  <Form id="formThemNgayApDung" form={formThemNgayApDung} layout="vertical">
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "flex-end"}}>
                      {renderFormInputColum({...ngay_ad}, 24)}

                      <Button type="primary" onClick={handleSubmit} style={{marginLeft: 8, marginBottom: 8}}>
                        Áp dụng
                      </Button>
                    </div>
                  </Form>
                </div>
              )}
              placement="topRight">
              <Button className="w-full" type="primary" icon={<PlusCircleOutlined />} onClick={() => setDropdownOpen(true)}>
                Thêm ngày áp dụng
              </Button>
            </Dropdown>
          </Space>
        </Form.Item>
      </div>
    );
  };

  const renderTableCauHoiApDung = () => {
    return (
      <Table<TableNgayApDungDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer", borderBottom: "1px solid #f0f0f0"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              background: record.ngay_ad === selectedNgayApDung ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleNgayApDungRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(ngayApDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiApDungProps(item.key as keyof TableNgayApDungDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
        scroll={dataTableListNgayApDung.length > pageSize ? {y: 215} : undefined}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => {
    return (
      <>
        {renderFormBHXH()}
        {renderFormBHYT()}
      </>
    );
  };
  const renderFormBHXH = () => {
    return (
      <div className="section-wrapper">
        <div className="section-title">Bảo hiểm xã hội tự nguyện</div>
        <Form form={formBHXH} layout="vertical" style={{padding: "16px 16px"}}>
          <Row gutter={16}>
            {renderFormInputColum({...ty_le_nsnn}, 12)}
            {renderFormInputColum({...ty_le_nsdp}, 12)}
          </Row>
        </Form>
      </div>
    );
  };

  const renderFormBHYT = () => {
    return (
      <div className="section-wrapper">
        <div className="section-title">Bảo hiểm y tế</div>
        <Form form={formBHYT} layout="vertical" style={{padding: "16px 16px"}}>
          <Row gutter={16}>
            {renderFormInputColum({...ty_le_nsnn}, 12)}
            {renderFormInputColum({...ty_le_nsdp}, 12)}
          </Row>
        </Form>
      </div>
    );
  };
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0" style={{display: "flex", justifyContent: "end"}}>
        <Button type="default" htmlType="reset" form="formChiTietNganSachHoTro" onClick={() => closeModal()} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressLuuNganSachHoTro}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={7} style={{paddingRight: 16}}>
          {renderTableCauHoiApDung()}
          {renderTableCauHoiApDungFooter()}
        </Col>
        <Col span={17} style={{justifyContent: "space-between", display: "flex", flexDirection: "column"}}>
          {renderForm()}
          {renderFooter()}
        </Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietTinhThanh ? `Chi tiết ngân sách hỗ trợ của ${chiTietTinhThanh.ten}` : ""} trang_thai={chiTietTinhThanh?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="50vw"
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={null}
        className="modal-ct-ngan-sach-ho-tro [&_.ant-space]:w-full">
        {renderTable()}
      </Modal>
    </Flex>
  );
});
ModalChiTietNganSachHoTroComponent.displayName = "ModalChiTietNganSachHoTroComponent";
export const ModalChiTietNganSachHoTro = memo(ModalChiTietNganSachHoTroComponent, isEqual);
