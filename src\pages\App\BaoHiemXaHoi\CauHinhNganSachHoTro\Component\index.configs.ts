import {IFormInput} from "@src/@types";
import {defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {TableProps} from "antd";

export interface IModalChiTietNganSachHoTroRef {
  open: (data?: any) => void;
  close: () => void;
}
export interface Props {}
//cấu hình bảng ngày áp dụng
export interface TableNgayApDungDataType {
  key: string;
  stt: number;
  ngay_ad: string;

  hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const ngayApDungColumns: TableProps<TableNgayApDungDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Ngày áp dụng",
    dataIndex: "ngay_ad",
    key: "ngay_ad",
  },
  {
    title: "Xóa",
    dataIndex: "hanh_dong",
    // key: "hanh_dong",
    width: 50,

    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type DataIndexNgayApDung = keyof TableNgayApDungDataType;
//FormThemNgayApDung
export interface IFormThemNgayApDungFieldsConfig {
  ngay_ad?: IFormInput;
}
export const FormThemNgayApDung: IFormThemNgayApDungFieldsConfig = {
  ngay_ad: {
    component: "date-picker",
    name: "ngay_ad",
    placeholder: "Ngày áp dụng",
    label: "Ngày áp dụng", // cho label vào thì sẽ thành input with label
    rules: [ruleInputMessage.required],
  },
};
export interface IFormChiTietNganSachHoTroFieldsConfig {
  ty_le_nsnn?: IFormInput;
  ty_le_nsdp?: IFormInput;
  ma_sp?: IFormInput;
}

export const FormChiTietNganSachHoTro: IFormChiTietNganSachHoTroFieldsConfig = {
  ty_le_nsnn: {
    component: "input-price",
    name: "ty_le_nsnn",
    placeholder: "Tỷ lệ ngân sách nhà nước",
    label: "Tỷ lệ ngân sách nhà nước (%)", // cho label vào thì sẽ thành input with label
    // rules: [ruleInputMessage.required],
  },
  ty_le_nsdp: {
    component: "input-price",
    name: "ty_le_nsdp",
    placeholder: "Tỷ lệ ngân sách địa phương",
    label: "Tỷ lệ ngân sách địa phương (%)", // cho label vào thì sẽ thành input with label
    // rules: [ruleInputMessage.required],
  },
  ma_sp: {
    className: "hidden",
    name: "ma_sp",
  },
};
