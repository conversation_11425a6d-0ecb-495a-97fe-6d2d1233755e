import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Popcomfirm} from "@src/components";
import {Col, Flex, Form, Modal, Row} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useState} from "react";
import dayjs from "dayjs";
import {FormTaoMoiTaiKhoanDonViThuHo, TRANG_THAI_TAO_MOI_TAI_KHOAN_DON_VI_THU_HO} from "../index.configs";
import {useQuanLyTaiKhoanDonViThuHoContext} from "../index.context";
import {ChiTietTaiKhoanDonViThuHoProps, IModalChiTietTaiKhoanDonViThuHoRef} from "./Constant";

//functions để xử lý date conversion
const convertNumberToDateJs = (numberDate: number): dayjs.Dayjs | null => {
  if (!numberDate) return null;
  const dateStr = numberDate.toString();
  if (dateStr.length === 8) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return dayjs(`${year}-${month}-${day}`);
  }
  return null;
};

const convertDateJsToNumber = (dayjsDate: dayjs.Dayjs): number => {
  if (!dayjsDate || !dayjs.isDayjs(dayjsDate)) return 0;
  return parseInt(dayjsDate.format('YYYYMMDD'));
};

const {ma, ten, ma_dvi, mat_khau, email, dthoai, ngay_hl, ngay_kt, stt, trang_thai} = FormTaoMoiTaiKhoanDonViThuHo;

const ModalChiTietTaiKhoanDonViThuHoComponent = forwardRef<IModalChiTietTaiKhoanDonViThuHoRef, ChiTietTaiKhoanDonViThuHoProps>(({listTaiKhoanDonViThuHo}: ChiTietTaiKhoanDonViThuHoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataTaiKhoanDonViThuHo?: CommonExecute.Execute.ITaiKhoanDonViThuHo) => {
      // Modal đang mở

      setIsOpen(true);
      if (dataTaiKhoanDonViThuHo) {
        setChiTietTaiKhoanDonViThuHo(dataTaiKhoanDonViThuHo); // nếu có dữ liệu -> set chi tiết TaiKhoanDonViThuHo -> là sửa
      } else {
        setChiTietTaiKhoanDonViThuHo(null);
        form.resetFields();
      }
    },
    close: () => setIsOpen(false),
  }));
  const [chiTietTaiKhoanDonViThuHo, setChiTietTaiKhoanDonViThuHo] = useState<CommonExecute.Execute.ITaiKhoanDonViThuHo | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {capNhatChiTietTaiKhoanDonViThuHo, getListTaiKhoanDonViThuHo, listDonVi, loading} = useQuanLyTaiKhoanDonViThuHoContext();

  useEffect(() => {
    if (isOpen && (!listDonVi || listDonVi.length === 0)) {
      console.log("Không hiển thị");
    }
  }, [isOpen, listDonVi]);
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);

  const donViOptions = useMemo(() => {
    if (!listDonVi || !Array.isArray(listDonVi)) {
      console.log("listDonVi không hợp lệ:", listDonVi);
      return [];
    }
    // chỉ lấy những dv có trạng thái D
    const filtered = listDonVi.filter(item => {
      const trangThai = item.trang_thai?.toString();
      const ma = item.ma?.toString();
      return trangThai === "D" && ma && ma.trim() !== "";
    });
    return filtered.map((item: any) => ({
      ma: item.ma || "",
      ten: item.ten || "Không có tên",
    }));
  }, [listDonVi]);

  const formValues = Form.useWatch([], form);
  // init form data - load dữ liệu vào form khi sửa
  useEffect(() => {
    if (chiTietTaiKhoanDonViThuHo && isOpen) {
      // Chỉnh sửa - load data vào form
      const arrFormData = [];
      for (const key in chiTietTaiKhoanDonViThuHo) {
        let value: any = chiTietTaiKhoanDonViThuHo[key as keyof CommonExecute.Execute.ITaiKhoanDonViThuHo];

        // Xử lý đặc biệt cho date fields - convert từ YYYYMMDD NUMBER sang dayjs
        if ((key === "ngay_hl" || key === "ngay_kt") && value) {
          if (typeof value === "number") {
            value = convertNumberToDateJs(value);
          } else if (typeof value === "string") {
            value = dayjs(value);
          }
        }

        arrFormData.push({
          name: key,
          value: value,
        });
      }
      form.setFields(arrFormData);
    } else if (!chiTietTaiKhoanDonViThuHo && isOpen) {
      // Tạo mới - set giá trị mặc định
      form.setFields([
        {name: "ma", value: ""},
        {name: "ten", value: ""},
        {name: "ma_dvi", value: undefined},
        {name: "mat_khau", value: ""},
        {name: "email", value: ""},
        {name: "dthoai", value: ""},
        {name: "ngay_hl", value: undefined},
        {name: "ngay_kt", value: undefined},
        {name: "stt", value: ""},
        {name: "trang_thai", value: "D"},
      ]);
    }
  }, [chiTietTaiKhoanDonViThuHo, isOpen, form]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setChiTietTaiKhoanDonViThuHo(null);
    form.resetFields();
  }, []);

  //xử lý submit form
  const onConfirm = async () => {
    try {
      //Validate form trước khi submit
      await form.validateFields();

      const values: ReactQuery.ICapNhatTaiKhoanDonViThuHoParams = form.getFieldsValue(); //lấy ra values của form

      // Xử lý convert date thành format YYYYMMDD (NUMBER) cho stored procedure
      if (values.ngay_hl && dayjs.isDayjs(values.ngay_hl)) {
        values.ngay_hl = convertDateJsToNumber(values.ngay_hl);
      }

      if (values.ngay_kt && dayjs.isDayjs(values.ngay_kt)) {
        values.ngay_kt = convertDateJsToNumber(values.ngay_kt);
      }
      // Xác định đây là EDIT hay CREATE mode
      const isEditMode = !!chiTietTaiKhoanDonViThuHo;

      await capNhatChiTietTaiKhoanDonViThuHo(values, isEditMode); //cập nhật lại TaiKhoanDonViThuHo
      await getListTaiKhoanDonViThuHo(); //lấy lại danh sách TaiKhoanDonViThuHo
      closeModal();
    } catch (error: any) {
      console.log("onConfirm error:", error);
      // Nếu là validation error, không đóng modal
      if (error?.errorFields) {
        console.log("Validation errors:", error.errorFields);
      }
    }
  };

  // RENDER
  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button type="default" onClick={() => closeModal()} className="mr-2 w-40" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Popcomfirm
          title={disableSubmit ? "Vui lòng nhập đầy đủ thông tin" : ""}
          onConfirm={onConfirm}
          okText="Lưu"
          description="Bạn có chắc muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonClassName="w-40"
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderFormColum = (props: IFormInput, span: number = 12) => (
    <Col span={span}>
      <FormInput {...props} id={`modal_${props.name}`} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical">
      <Row gutter={16}>
        {renderFormColum({...ma, disabled: chiTietTaiKhoanDonViThuHo ? true : false}, 8)}
        {renderFormColum({...ten}, 10)}
        {renderFormColum({...mat_khau}, 6)}
        
      </Row>
      <Row gutter={16}>
        {renderFormColum(
          {
            ...ma_dvi,
            options: donViOptions,
            fieldNames: {label: "ten", value: "ma"}, 
            // Debug: log khi render
            // onFocus: () => console.log("[Modal] ma_dvi focused, options:", donViOptions),
          },8)
        }
        {renderFormColum({...email}, 10)}
        {renderFormColum({...dthoai}, 6)}
      </Row>
      <Row gutter={16}>
        {renderFormColum({...ngay_hl, format: "DD/MM/YYYY"}, 8)}
        {renderFormColum({...ngay_kt, format: "DD/MM/YYYY"}, 6)}
        {renderFormColum({...stt}, 4)}
        {renderFormColum(
          {
            ...trang_thai,
            options: TRANG_THAI_TAO_MOI_TAI_KHOAN_DON_VI_THU_HO,
            fieldNames: {label: "ten", value: "ma"},
          },6
        )}
      </Row>
    </Form>
  );
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={
          <HeaderModal
            title={chiTietTaiKhoanDonViThuHo ? `${chiTietTaiKhoanDonViThuHo.ten}` : "Tạo mới Tài khoản đơn vị thu hộ bhxh"}
            trang_thai_ten={chiTietTaiKhoanDonViThuHo?.trang_thai_ten}
            trang_thai={chiTietTaiKhoanDonViThuHo?.trang_thai}
          />
        }
        // centered
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={850}
        styles={{
          body: {
            paddingTop: "8px",
            paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderForm()}
      </Modal>
    </Flex>
  );
});

ModalChiTietTaiKhoanDonViThuHoComponent.displayName = "ModalChiTietTaiKhoanDonViThuHoComponent";
export const ModalChiTietTaiKhoanDonViThuHo = memo(ModalChiTietTaiKhoanDonViThuHoComponent, isEqual);
