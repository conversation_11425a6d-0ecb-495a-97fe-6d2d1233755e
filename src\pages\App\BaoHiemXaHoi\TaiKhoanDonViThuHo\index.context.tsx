import {createContext, useContext} from "react";

import {IQuanLyTaiKhoanDonViThuHoContextProps} from "./index.model";

//khởi tạo giá trị mặc định
export const QuanLyTaiKhoanDonViThuHoContext = createContext<IQuanLyTaiKhoanDonViThuHoContextProps>({
  listTaiKhoanDonViThuHo: [],
  listDonVi: [],
  tongSoDong: 0,
  loading: false,
  filterParams: {},
  getListTaiKhoanDonViThuHo: async () => Promise.resolve(),
  getChiTietTaiKhoanDonViThuHo: async () => Promise.resolve({} as CommonExecute.Execute.ITaiKhoanDonViThuHo),
  capNhatChiTietTaiKhoanDonViThuHo: async () => Promise.resolve(),
  setFilterParams: () => {},
});

export const useQuanLyTaiKhoanDonViThuHoContext = () => useContext(QuanLyTaiKhoanDonViThuHoContext);