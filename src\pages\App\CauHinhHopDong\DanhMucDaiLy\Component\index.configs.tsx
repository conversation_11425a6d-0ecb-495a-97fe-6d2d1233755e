import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietDanhMucDaiLyFieldsConfig {
  // ma_doi_tac: IFormInput;
  // ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  loai: IFormInput;
  ten: IFormInput;
  nguoi_dd: IFormInput;
  dthoai_dd: IFormInput;
  email_dd: IFormInput;
  mst_dd: IFormInput;
  cmt_dd: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  trang_thai_ten: IFormInput;
  ma_ct: IFormInput;
}
export const FormChiTietDanhMucDaiLy: IFormChiTietDanhMucDaiLyFieldsConfig = {
  // ma_doi_tac_ql: {
  //   component: "select",
  //   label: "Đối tác",
  //   name: "ma_doi_tac_ql",
  //   placeholder: "Chọn đối tác",
  //   rules: [ruleRequired],
  // },
  // ma_doi_tac: {
  //   component: "input",
  //   label: "Đối tác",
  //   name: "ma_doi_tac",
  //   placeholder: "Chọn đối tác",
  //   rules: [ruleRequired],
  // },
  ma: {
    component: "input",
    label: "Mã đại lý",
    name: "ma",
    placeholder: "Mã đại lý",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên đại lý",
    name: "ten",
    placeholder: "Tên đại lý",
    rules: [ruleRequired],
  },
  loai: {
    component: "select",
    label: "Loại đại lý",
    name: "loai",
    placeholder: "Chọn loại đại lý",
    rules: [ruleRequired],
  },
  nguoi_dd: {
    component: "input",
    label: "Tên người / tổ chức đại diện",
    name: "nguoi_dd",
    placeholder: "Tên người / tổ chức đại diện",
    rules: [ruleRequired],
  },
  dthoai_dd: {
    component: "input",
    label: "SĐT người/tổ chức đại diện",
    name: "dthoai_dd",
    placeholder: "SĐT người/tổ chức đại diện",
    // rules: [ruleRequired],
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_PHONE,
        message: "Số điện thoại sai định dạng",
      },
    ],
  },
  email_dd: {
    component: "input",
    label: "Email người/ tổ chức đại diện",
    placeholder: "Email người/ tổ chức đại diện",
    name: "email_dd",
    rules: [
      {
        pattern: REGUlAR_EXPRESSION.REG_EMAIL,
        message: "Email sai định dạng",
      },
    ],
  },
  mst_dd: {
    component: "input",
    label: "Mã số thuế tổ chức",
    placeholder: "Mã số thuế tổ chức",
    name: "mst_dd",
  },
  cmt_dd: {
    component: "input",
    label: "CMT/CCCD cá nhân",
    placeholder: "CMT/CCCD cá nhân",
    name: "cmt_dd",
  },
  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  trang_thai_ten: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai_ten",
    placeholder: "Chọn trạng thái",
  },
  ma_ct: {
    component: "select",
    label: "Đại lý cấp trên",
    name: "ma_ct",
    placeholder: "Chọn đại lý cấp trên",
    showSearch: false,
  },
};

export interface ThemDaiLyProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
}
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalThemDanhMucDaiLyRef {
  open: (data?: CommonExecute.Execute.IDanhMucDaiLy) => void;
  close: () => void;
}
export interface IModalTimDaiLyChaRef {
  open: (data?: CommonExecute.Execute.IDanhMucDaiLy) => void;
  close: () => void;
}

//Danh mục đại lý cha
export interface TableDanhMucDaiLyChaDataType {
  key: string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  doi_tac_ql_ten_tat?: string;
  ten?: string;
  doi_tac_ql?: string;
  loai?: string;
  ten_loai?: string;
  ten_dd?: string;
  so_dien_thoai?: string;
  email?: string;
  mst?: string;
  cmt?: string;
  trang_thai?: string;
  ma_ct?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhMucDaiLyChaColumns: TableProps<TableDanhMucDaiLyChaDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Loại đại lý", dataIndex: "ten_loai", key: "ten_loai", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên đại diện", dataIndex: "nguoi_dd", key: "nguoi_dd", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Số điện thoại", dataIndex: "dthoai_dd", key: "dthoai_dd", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Email", dataIndex: "email_dd", key: "email_dd", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Mã số thuế", dataIndex: "mst_dd", key: "mst_dd", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "CMT/CCCD", dataIndex: "cmt_dd", key: "cmt_dd", width: 120, align: "center", ...defaultTableColumnsProps},
];
// export type DataIndex = keyof TableDanhMucDaiLyDataType;
export type DataIndexCha = keyof TableDanhMucDaiLyChaDataType;
//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableDaiLyChaColumnDataIndex = keyof TableDanhMucDaiLyChaDataType;
export interface DaiLyChaProps {
  onSelectDaiLyCha: (ma_ct: CommonExecute.Execute.IDanhMucDaiLy | null) => void;
  chiTietDaiLy: CommonExecute.Execute.IChiTietDanhMucDaiLy | null;
}
export interface IDaiLySelected {
  ma?: string;
  ten?: string;
}

//Tab đơn vị quản lý
export interface TableDaiLyQuanLyDataType {
  key: string;
  stt?: number;
  ten_chi_nhanh_ql?: string;
  ten_doi_tac_ql?: string;
  ma_chi_nhanh_ql?: string;
  ma_doi_tac_ql?: string;
  is_checked: string;
}
export const daiLyQuanLyColumns: TableProps<TableDaiLyQuanLyDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {title: "Đối tác quản lý", dataIndex: "ten_doi_tac_ql", key: "ten_doi_tac_ql", ...defaultTableColumnsProps},
  {title: "Chi nhánh quản lý", dataIndex: "ten_chi_nhanh_ql", key: "ten_chi_nhanh_ql", ...defaultTableColumnsProps},
  {title: "Chọn", dataIndex: "is_checked", key: "is_checked", width: 60, ...defaultTableColumnsProps},
];
export type DataIndexDLQL = keyof TableDaiLyQuanLyDataType;
