import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useImperativeHandle} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {listGioiTinhSelect, listKhachHangVipSelect, PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiemProps, PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiemRef} from "./Constant";

const TabThongTinNguoiDuocBaoHiemComponent = forwardRef<PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiemRef, PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiemProps>(
  ({}: PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiemProps, ref) => {
    useImperativeHandle(ref, () => ({}));

    const {listGoiBaoHiem, chiTietNguoiDuocBaoHiem} = useHopDongConNguoiContext();

    // Helper function để tìm tên từ danh sách options
    const findOptionName = (value: string | undefined, options: any[], keyField = "ma", nameField = "ten") => {
      if (!value || !options) return value;
      const found = options.find(item => item[keyField] === value);
      return found ? found[nameField] : value;
    };

    // Helper function để format ngày
    const formatDate = (dateValue: string | number | undefined) => {
      if (!dateValue) return undefined;
      try {
        return dayjs(dateValue).format("DD/MM/YYYY");
      } catch {
        return String(dateValue);
      }
    };

    // Helper function để format giờ
    const formatTime = (timeValue: string | undefined) => {
      if (!timeValue) return undefined;
      try {
        return dayjs(timeValue, "HH:mm").format("HH:mm");
      } catch {
        return String(timeValue);
      }
    };

    return (
      <div className="max-h-[70vh] overflow-y-auto p-4">
        {chiTietNguoiDuocBaoHiem && (
          <div className="rounded border border-[#d9d9d9] bg-[#f6f6f6] p-3 shadow-sm">
            {(() => {
              // Chia thành 2 cột thẳng hàng
              const col1 = [
                {label: "Tên người được bảo hiểm", value: chiTietNguoiDuocBaoHiem.ten || "-"},
                {label: "Ngày sinh", value: formatDate(chiTietNguoiDuocBaoHiem.ngay_sinh) || "-"},
                {label: "Giới tính", value: findOptionName(chiTietNguoiDuocBaoHiem.gioi_tinh, listGioiTinhSelect) || "-"},
                {label: "Số CMT/CCCD/HC", value: chiTietNguoiDuocBaoHiem.so_cmt || "-"},
                {label: "Điện thoại", value: chiTietNguoiDuocBaoHiem.dthoai || "-"},
                {label: "Email", value: chiTietNguoiDuocBaoHiem.email || "-"},
                {label: "Địa chỉ", value: chiTietNguoiDuocBaoHiem.dia_chi || "-"},
                {label: "Nhóm gói bảo hiểm", value: findOptionName(chiTietNguoiDuocBaoHiem.ma_goi_bh, listGoiBaoHiem) || "-"},
              ];

              const col2 = [
                {label: "Ngày cấp", value: formatDate(chiTietNguoiDuocBaoHiem.ngay_cap) || "-"},
                {label: "Giấy chứng nhận", value: chiTietNguoiDuocBaoHiem.gcn || "-"},
                {label: "VIP", value: findOptionName(chiTietNguoiDuocBaoHiem.vip, listKhachHangVipSelect) || "-"},
                {label: "Giờ hiệu lực", value: formatTime(chiTietNguoiDuocBaoHiem.gio_hl) || "-"},
                {label: "Ngày hiệu lực", value: formatDate(chiTietNguoiDuocBaoHiem.ngay_hl) || "-"},
                {label: "Giờ kết thúc", value: formatTime(chiTietNguoiDuocBaoHiem.gio_kt) || "-"},
                {label: "Ngày kết thúc", value: formatDate(chiTietNguoiDuocBaoHiem.ngay_kt) || "-"},
              ];

              return (
                <div>
                  <div className="grid grid-cols-2 gap-4">
                    {/* Cột 1 */}
                    <div className="flex flex-col gap-2">
                      {col1.map((item: any, idx: number) => (
                        <div key={idx} className="text-[14px]">
                          <b>{item.label}:</b> {item.value}
                        </div>
                      ))}
                    </div>

                    {/* Cột 2 */}
                    <div className="flex flex-col gap-2">
                      {col2.map((item: any, idx: number) => (
                        <div key={idx} className="text-[14px]">
                          <b>{item.label}:</b> {item.value}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Thông tin đơn vị công tác */}
                  <div className="mt-4">
                    <h3 className="my-0 mb-[8px] text-lg">Thông tin đơn vị công tác</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {/* Cột 1 - Công ty và Chi nhánh */}
                      <div className="flex flex-col gap-2">
                        <div className="text-[14px]">
                          <b>Công ty công tác:</b> {chiTietNguoiDuocBaoHiem.cty_ctac || "-"}
                        </div>
                        <div className="text-[14px]">
                          <b>Chi nhánh công tác:</b> {chiTietNguoiDuocBaoHiem.cnhanh_ctac || "-"}
                        </div>
                        <div className="text-[14px]">
                          <b>Phòng ban công tác:</b> {chiTietNguoiDuocBaoHiem.pban_ctac || "-"}
                        </div>
                      </div>

                      {/* Cột 2 - Chức vụ và Nhân viên */}
                      <div className="flex flex-col gap-2">
                        <div className="text-[14px]">
                          <b>Chức vụ công tác:</b> {chiTietNguoiDuocBaoHiem.cvu_ctac || "-"}
                        </div>
                        <div className="text-[14px]">
                          <b>Mã nhân viên công tác:</b> {chiTietNguoiDuocBaoHiem.ma_nv_ctac || "-"}
                        </div>
                        <div className="text-[14px]">
                          <b>Email công tác:</b> {chiTietNguoiDuocBaoHiem.email_ctac || "-"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
      </div>
    );
  },
);

TabThongTinNguoiDuocBaoHiemComponent.displayName = "TabThongTinNguoiDuocBaoHiemComponent";
export const PheDuyetHopDongStep2_TabThongTinNguoiDuocBaoHiem = memo(TabThongTinNguoiDuocBaoHiemComponent, isEqual);
