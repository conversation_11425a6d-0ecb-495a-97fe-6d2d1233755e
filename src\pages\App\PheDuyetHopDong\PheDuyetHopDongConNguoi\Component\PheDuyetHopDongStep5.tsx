import {FilePdfOutlined} from "@ant-design/icons";
import {Viewer, Worker} from "@react-pdf-viewer/core";
import "@react-pdf-viewer/core/lib/styles/index.css";
import {zoomPlugin} from "@react-pdf-viewer/zoom";
import "@react-pdf-viewer/zoom/lib/styles/index.css";
import {ReactQuery} from "@src/@types";
import {Button} from "@src/components";
import {useAsyncAction} from "@src/hooks/useAsyncAction";
import {formatCurrencyUS} from "@src/utils";
import {Col, message, Row, Tabs} from "antd";
import {isEqual} from "lodash";
import workerUrl from "pdfjs-dist/build/pdf.worker.js?url";
import {memo, useCallback, useEffect, useImperativeHandle, useRef, useState, forwardRef} from "react";
import {useHopDongConNguoiContext} from "@src/pages/App/BaoHiemConNguoi/HopDongConNguoi/index.context";
import {IPheDuyetHopDongStep5Ref, TableNguoiDuocBaoHiemColumnDataType, PheDuyetHopDongStep5Props} from "./Constant";
import {DanhSachNguoiDuocBaoHiem} from "./PheDuyetHopDongStep5_DanhSachNguoiDuocBaoHiem";
import {ThongTinThanhToanStep4} from "./PheDuyetHopDongStep4_TabThongTinThanhToan";
import {ThongTinDongBaoHiemStep4} from "./PheDuyetHopDongStep4_TabThongTinDongBaoHiem";
import {ThongTinTaiBaoHiemStep4} from "./PheDuyetHopDongStep4_TabThongTinTaiBaoHiem";

// Custom hook để xử lý zoom và pan (loại bỏ điều hướng trang để tránh xung đột)
const usePdfZoomPan = (zoomPluginInstance: any, minZoom: number = 0.5) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({x: 0, y: 0});
  const [zoomLevel, setZoomLevel] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  // Xử lý cuộn chuột - Ctrl+Cuộn = Phóng to/thu nhỏ, Cuộn = Cuộn tự nhiên
  const handleWheel = useCallback(
    (e: WheelEvent) => {
      const isCtrlPressed = e.ctrlKey || e.metaKey;

      if (isCtrlPressed) {
        // Chế độ zoom khi giữ Ctrl
        e.preventDefault();
        const isScrollDown = e.deltaY > 0;
        const zoomStep = 0.1;
        const delta = isScrollDown ? -zoomStep : zoomStep;
        const newZoom = Math.max(minZoom, Math.min(5, zoomLevel + delta));

        if (zoomPluginInstance && zoomPluginInstance.zoomTo) {
          zoomPluginInstance.zoomTo(newZoom);
          setZoomLevel(newZoom);
        }
      }
      // Loại bỏ hoàn toàn logic tự động chuyển trang
      // Để PDF viewer xử lý cuộn tự nhiên mà không can thiệp
    },
    [zoomLevel, zoomPluginInstance, minZoom],
  );

  // Xử lý phóng to/thu nhỏ bằng bàn phím (loại bỏ điều hướng trang để tránh can thiệp)
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.target && (e.target as HTMLElement).tagName === "INPUT") return;

      // Chỉ xử lý phím tắt zoom, loại bỏ điều hướng trang để tránh xung đột

      // Phím tắt zoom
      let newZoom = zoomLevel;

      if (e.key === "+" || e.key === "=" || (e.ctrlKey && e.key === "+")) {
        e.preventDefault();
        newZoom = Math.min(5, zoomLevel + 0.2);
      } else if (e.key === "-" || (e.ctrlKey && e.key === "-")) {
        e.preventDefault();
        newZoom = Math.max(minZoom, zoomLevel - 0.2);
      } else if (e.key === "0" || (e.ctrlKey && e.key === "0")) {
        e.preventDefault();
        newZoom = 1;
      }

      if (newZoom !== zoomLevel && zoomPluginInstance && zoomPluginInstance.zoomTo) {
        zoomPluginInstance.zoomTo(newZoom);
        setZoomLevel(newZoom);
      }
    },
    [zoomLevel, zoomPluginInstance, minZoom],
  );

  // Xử lý double click để fit-to-width hoặc zoom 100%
  const handleDoubleClick = useCallback(() => {
    if (!zoomPluginInstance) return;

    const newZoom = zoomLevel === 1 ? 1.5 : 1; // Toggle giữa 100% và 150%
    zoomPluginInstance.zoomTo(newZoom);
    setZoomLevel(newZoom);
  }, [zoomLevel, zoomPluginInstance]);

  // Xử lý pan bằng chuột
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (zoomLevel > 1 && e.button === 0) {
        // Chỉ xử lý left click
        setIsPanning(true);
        setLastMousePos({x: e.clientX, y: e.clientY});
        e.preventDefault();
        e.stopPropagation();
      }
    },
    [zoomLevel],
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isPanning || !containerRef.current) return;

      const deltaX = e.clientX - lastMousePos.x;
      const deltaY = e.clientY - lastMousePos.y;

      // Tìm container có thể scroll của PDF viewer
      const scrollContainer = containerRef.current.querySelector(".rpv-core__inner-pages") || containerRef.current.querySelector(".rpv-core__viewer") || containerRef.current;

      if (scrollContainer && typeof (scrollContainer as any).scrollBy === "function") {
        (scrollContainer as any).scrollBy(-deltaX, -deltaY);
      } else if (scrollContainer) {
        (scrollContainer as any).scrollLeft -= deltaX;
        (scrollContainer as any).scrollTop -= deltaY;
      }

      setLastMousePos({x: e.clientX, y: e.clientY});
      e.preventDefault();
    },
    [isPanning, lastMousePos],
  );

  const handleMouseUp = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning) {
        setIsPanning(false);
        e.preventDefault();
      }
    },
    [isPanning],
  );

  // Xử lý mouse leave để dừng panning
  const handleMouseLeave = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Global mouse up handler cho document
  const handleGlobalMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Effect để thêm event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("wheel", handleWheel, {passive: false});
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("mouseup", handleGlobalMouseUp);

    return () => {
      container.removeEventListener("wheel", handleWheel);
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
    };
  }, [handleWheel, handleKeyDown, handleGlobalMouseUp]);

  return {
    containerRef,
    isPanning,
    zoomLevel,
    currentPage,
    totalPages,
    handleDoubleClick,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    setZoomLevel,
    setCurrentPage,
    setTotalPages,
  };
};

const PheDuyetHopDongStep5Component = forwardRef<IPheDuyetHopDongStep5Ref, PheDuyetHopDongStep5Props>(({onDownloadPDF}: PheDuyetHopDongStep5Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {chiTietHopDong, timKiemPhanTrangNguoiDuocBaoHiemParams, getChiTietNguoiDuocBaoHiem, setTimKiemPhanTrangNguoiDuocBaoHiemParams, exportPdfHopDong} = useHopDongConNguoiContext();

  // State lưu selectedFile dựa trên categories thực tế
  const [selectedFile, setSelectedFile] = useState<any>(undefined);

  // Zoom plugin cho PDF Viewer
  const zoomPluginInstance = zoomPlugin();

  // State để lưu totalPages và tính toán minZoom
  const [totalPages, setTotalPages] = useState(0);
  const minZoom = totalPages <= 1 ? 0.8 : 0.5; // 80% nếu 1 page, 50% nếu nhiều hơn 1 page

  // Sử dụng custom hook cho zoom, pan và page navigation
  const {containerRef, isPanning, zoomLevel, handleDoubleClick, handleMouseDown, handleMouseMove, handleMouseUp, handleMouseLeave, setZoomLevel, setCurrentPage} = usePdfZoomPan(
    zoomPluginInstance,
    minZoom,
  );

  // Effect để reset zoom và page info khi file thay đổi
  useEffect(() => {
    if (selectedFile) {
      setZoomLevel(1);
      setCurrentPage(1);
      setTotalPages(0);
    }
  }, [selectedFile, setZoomLevel, setCurrentPage]);

  // Effect để đảm bảo zoom level không vi phạm minZoom khi totalPages thay đổi
  useEffect(() => {
    if (zoomLevel < minZoom && zoomPluginInstance && zoomPluginInstance.zoomTo) {
      zoomPluginInstance.zoomTo(minZoom);
      setZoomLevel(minZoom);
    }
  }, [minZoom, zoomLevel, zoomPluginInstance, setZoomLevel]);

  //khởi tạo dữ liệu();
  useEffect(() => {
    initData();
  }, []);

  const initData = useCallback(() => {
    setTimKiemPhanTrangNguoiDuocBaoHiemParams({...timKiemPhanTrangNguoiDuocBaoHiemParams, so_id: chiTietHopDong?.so_id, so_hd: chiTietHopDong?.so_hd, trang: 1, so_dong: 10});
  }, [timKiemPhanTrangNguoiDuocBaoHiemParams, chiTietHopDong]);

  // Ghi nhớ hàm async để tránh vòng lặp vô hạn
  const viewHopDongAsyncFn = useCallback(async () => {
    if (!chiTietHopDong?.so_id) {
      message.error("Không có thông tin hợp đồng.");
      return;
    }

    const templateHopDong = chiTietHopDong?.file_hd;
    if (!templateHopDong) {
      message.error("File mẫu in chưa được cấu hình trên hệ thống");
      return;
    }

    try {
      const paramsXemFileHD = {
        ma_doi_tac_ql: "ESCS",
        so_id: chiTietHopDong.so_id,
        template: templateHopDong || "", // Template sẽ được xử lý bởi backend
      };
      const pdfUrl = await exportPdfHopDong(paramsXemFileHD);
      if (pdfUrl) {
        // Cập nhật selectedFile để hiển thị PDF hợp đồng
        setSelectedFile({
          type: "pdf",
          url: pdfUrl,
          name: `Hợp đồng ${chiTietHopDong.so_hd || chiTietHopDong.so_id}`,
        });
        // message.success("Tải hợp đồng thành công!");
      } else {
        console.error("❌ pdfUrl is null or undefined");
      }
    } catch (error) {
      console.error("Lỗi khi tải hợp đồng PDF:", error);
      message.error("Có lỗi xảy ra khi tải hợp đồng PDF!");
      throw error; // Re-throw để useAsyncAction có thể handle
    }
  }, [chiTietHopDong?.so_id, chiTietHopDong?.so_hd, exportPdfHopDong]);

  // Triển khai async action cho việc xem hợp đồng PDF sử dụng useAsyncAction hook
  const [handleViewHopDong, isLoadingViewHopDong] = useAsyncAction(viewHopDongAsyncFn);

  // Ghi nhớ hàm async cho GCN để tránh vòng lặp vô hạn
  const viewGCNAsyncFn = useCallback(
    async (record: TableNguoiDuocBaoHiemColumnDataType) => {
      if (!record?.so_id_dt) {
        message.error("Không có thông tin đối tượng.");
        return;
      }

      const templateGCN = chiTietHopDong?.file_gcn;
      if (!templateGCN) {
        message.error("File mẫu in chưa được cấu hình trên hệ thống");
        return;
      }

      try {
        const paramsXemFileGCN = {
          ma_doi_tac_ql: "ESCS",
          so_id: chiTietHopDong?.so_id,
          so_id_dt: record?.so_id_dt ? Number(record.so_id_dt) : undefined,
          template: templateGCN || "", // Template sẽ được xử lý bởi backend
        };
        const pdfUrl = await exportPdfHopDong(paramsXemFileGCN);
        if (pdfUrl) {
          // Cập nhật selectedFile để hiển thị PDF GCN
          setSelectedFile({
            type: "pdf",
            url: pdfUrl,
            name: `GCN ${record?.so_id_dt}`,
          });
          // message.success("Tải giấy chứng nhận thành công!");
        } else {
          console.error("❌ pdfUrl is null or undefined");
        }
      } catch (error) {
        console.error("Lỗi khi tải GCN PDF:", error);
        message.error("Có lỗi xảy ra khi tải giấy chứng nhận PDF!");
        throw error; // Re-throw để useAsyncAction có thể handle
      }
    },
    [chiTietHopDong, exportPdfHopDong],
  );

  // Triển khai async action cho việc xem GCN PDF sử dụng useAsyncAction hook
  const [handleViewGCN] = useAsyncAction(viewGCNAsyncFn);

  //bấm chọn 1 đối tượng - hiển thị GCN
  const handleSelectDoiTuongBaoHiemXe = useCallback(
    async (record: TableNguoiDuocBaoHiemColumnDataType) => {
      if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
      await Promise.all([getChiTietNguoiDuocBaoHiem(record as ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi), handleViewGCN(record)]);
    },
    [getChiTietNguoiDuocBaoHiem, handleViewGCN],
  );

  // Tự động hiển thị hợp đồng khi vào màn hình
  useEffect(() => {
    if (chiTietHopDong?.so_id) {
      handleViewHopDong();
    }
  }, [chiTietHopDong?.so_id]); // Chỉ phụ thuộc vào so_id để tránh vòng lặp

  // Hàm xử lý download PDF
  const handleDownloadPDF = () => {
    if (selectedFile && selectedFile.type === "pdf" && selectedFile.url) {
      const link = document.createElement("a");
      link.href = selectedFile.url;
      link.download = selectedFile.name ? `${selectedFile.name}.pdf` : "file.pdf";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      message.warning("Không có file PDF để tải xuống.");
    }
  };

  // Effect để thông báo cho parent component về download function
  useEffect(() => {
    if (onDownloadPDF) {
      const hasFile = selectedFile && selectedFile.type === "pdf" && selectedFile.url;
      onDownloadPDF(handleDownloadPDF, !!hasFile);
    }
  }, [selectedFile, onDownloadPDF]);

  // RENDER
  return (
    <Row gutter={16} className="mt-4 h-full overflow-hidden border border-[#eee]">
      {/* Cột trái: PDF Viewer với zoom/pan */}
      <Col span={11} className="flex h-[90%] flex-col overflow-hidden !pl-0" style={{borderBottom: "1px solid #eee", borderLeft: "1px solid #eee"}}>
        <div className="relative flex min-h-[65vh] flex-col overflow-hidden">
          {selectedFile && selectedFile.type === "pdf" ? (
            <div className="flex h-full w-full flex-col">
              {/* Zoom controls với thông tin zoom level và page navigation */}

              {/* PDF Viewer với zoom/pan tương tác */}
              <div
                ref={containerRef}
                className={`min-h-0 flex-1 overflow-hidden rounded-b-lg border border-[#333] shadow-[0_2px_8px_rgba(0,0,0,0.1)] ${
                  zoomLevel > 1 ? (isPanning ? "cursor-grabbing" : "cursor-grab") : "cursor-default"
                }`}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseLeave}
                onDoubleClick={handleDoubleClick}
                style={{
                  userSelect: "none",
                  WebkitUserSelect: "none",
                  MozUserSelect: "none",
                  msUserSelect: "none",
                }}>
                <Worker workerUrl={workerUrl}>
                  <Viewer
                    fileUrl={selectedFile.url}
                    plugins={[zoomPluginInstance]}
                    onDocumentLoad={e => {
                      // Reset zoom level và page info khi load document mới
                      setZoomLevel(1);
                      setCurrentPage(1);
                      setTotalPages(e.doc.numPages);
                    }}
                    onPageChange={e => {
                      // Cập nhật current page khi user navigate - chỉ để theo dõi, không can thiệp
                      setCurrentPage(e.currentPage + 1); // react-pdf-viewer sử dụng 0-based index
                    }}
                  />
                </Worker>
              </div>
            </div>
          ) : (
            <div className="flex h-full w-full flex-col">
              {/* Header placeholder để match với PDF viewer header */}
              <div className="flex items-center justify-between gap-2 rounded-t-lg border-b border-[#eee] bg-[#fafafa] p-2">
                <div className="flex items-center gap-2">
                  <div className="text-gray-600 bg-white rounded border px-2 py-1 text-xs">Chưa có PDF</div>
                </div>
              </div>
              {/* Main placeholder container để match với PDF viewer container */}
              <div className="flex min-h-0 flex-1 flex-col items-center justify-center overflow-hidden rounded-b-lg border border-[#333] text-center shadow-[0_2px_8px_rgba(0,0,0,0.1)]">
                <div style={{fontSize: 48, opacity: 0.5, color: "#999"}}>📄</div>
                <div style={{marginBottom: 8, color: "#999", fontSize: 16}}>Chưa có hợp đồng hoặc giấy chứng nhận nào được chọn</div>
              </div>
            </div>
          )}
        </div>
      </Col>

      {/* Cột giữa: Xem hình ảnh/PDF */}
      <Col span={7} className="flex h-[90%] flex-col overflow-hidden !px-0">
        {/* Thông tin chi tiết đối tượng xe được chọn */}
        {chiTietHopDong && (
          <div className="rounded border border-[#d9d9d9] bg-[#f6f6f6] p-3 shadow-sm">
            {/* <div style={{fontWeight: 600, fontSize: 16, marginBottom: 8}}>Thông tin đối tượng xe</div> */}
            {(() => {
              const thongTinHopDong = [
                {label: "Khách hàng", value: chiTietHopDong.ten_kh || "-"},
                {label: "Đối tác cấp đơn", value: chiTietHopDong.ten_doi_tac_ql || "-"},
                {label: "Số hợp đồng", value: chiTietHopDong.so_hd || "-"},
                {
                  label: "Hiệu lực",
                  value: chiTietHopDong.ngay_hl ? `${chiTietHopDong.gio_hl || ""} ${chiTietHopDong.ngay_hl} - ${chiTietHopDong.gio_kt || ""} ${chiTietHopDong.ngay_kt || ""}` : "-",
                },
                {label: "Loại hợp đồng", value: chiTietHopDong.vip || "Không Vip"},
                // Thêm các trường khác nếu cần
              ];

              // Thông tin sản phẩm và phí để hiển thị trên cùng một dòng
              const sanPham = chiTietHopDong.ten_sp || "-";
              const tongPhi = chiTietHopDong.tong_phi ? formatCurrencyUS(chiTietHopDong.tong_phi) : "-";
              return (
                <div>
                  <div className="flex flex-col gap-2">
                    {thongTinHopDong.map((item: any, idx: number) => (
                      <div key={idx} className="text-[11px]">
                        <b>{item.label}:</b> {item.value}
                      </div>
                    ))}
                    {/* Sản phẩm và Tổng phí BH trên cùng một dòng */}
                    <div className="flex gap-4 text-[11px]">
                      <span>
                        <b>Sản phẩm:</b> {sanPham}
                      </span>
                      <span>
                        <b>Tổng phí BH:</b> {tongPhi}
                      </span>
                    </div>
                  </div>
                  <div className="ml-0 mr-auto">
                    <Button
                      type="link"
                      className="!p-0 !font-semibold !text-[#7ac142]"
                      icon={<FilePdfOutlined />}
                      onClick={handleViewHopDong}
                      loading={isLoadingViewHopDong}
                      disabled={isLoadingViewHopDong}>
                      Xem hợp đồng
                    </Button>
                  </div>
                </div>
              );
            })()}
          </div>
        )}
        {/* Tabs cấu hình bảo hiểm con người */}
        <Tabs
          className="mt-3 [&_.ant-tabs-nav]:mb-0"
          animated={false}
          size="small"
          defaultActiveKey="1"
          items={[
            {
              key: "1",
              label: "Thông tin thanh toán",
              children: <ThongTinThanhToanStep4 />,
            },
            {
              key: "2",
              label: "Đồng bảo hiểm",
              children: <ThongTinDongBaoHiemStep4 />,
            },
            {
              key: "3",
              label: "Tái bảo hiểm",
              children: <ThongTinTaiBaoHiemStep4 />,
            },
          ]}
        />
      </Col>

      {/* Cột phải: Danh sách người được bảo hiểm */}
      <Col span={6} className="flex h-[90%] flex-col overflow-hidden">
        <DanhSachNguoiDuocBaoHiem onRowClick={handleSelectDoiTuongBaoHiemXe} />
      </Col>
    </Row>
  );
});

PheDuyetHopDongStep5Component.displayName = "PheDuyetHopDongStep5Component";
export const PheDuyetHopDongStep5 = memo(PheDuyetHopDongStep5Component, isEqual);
